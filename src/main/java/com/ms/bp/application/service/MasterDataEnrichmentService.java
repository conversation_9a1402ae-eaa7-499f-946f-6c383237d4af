package com.ms.bp.application.service;

import com.ms.bp.shared.common.db.JdbcTemplate;
import com.ms.bp.shared.common.io.cache.ImportSessionCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * マスタデータ補完アプリケーションサービス
 * インポート処理中のマスタデータ取得と補完を支援するアプリケーション層サービス
 * 
 * 主な機能：
 * - 批量查询优化
 * - キャッシュ機能
 * - エラーハンドリング
 */
public class MasterDataEnrichmentService {
    private static final Logger logger = LoggerFactory.getLogger(MasterDataEnrichmentService.class);

    /**
     * エリアコードからエリア名称を一括取得
     * 
     * @param areaCodes エリアコードのリスト
     * @param template JDBCテンプレート
     * @param cache セッションキャッシュ
     * @return エリアコードとエリア名称のマップ
     */
    public Map<String, String> getAreaNames(List<String> areaCodes, 
                                           JdbcTemplate template, 
                                           ImportSessionCache cache) {
        if (areaCodes == null || areaCodes.isEmpty()) {
            return new HashMap<>();
        }

        // 重複を除去
        List<String> uniqueAreaCodes = areaCodes.stream()
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (uniqueAreaCodes.isEmpty()) {
            return new HashMap<>();
        }

        // キャッシュから取得を試行
        Map<String, String> result = new HashMap<>();
        List<String> uncachedCodes = new ArrayList<>();

        for (String areaCode : uniqueAreaCodes) {
            String cacheKey = ImportSessionCache.generateKey("AREA", "AREA_CODE", areaCode);
            String cachedName = cache.get(cacheKey);
            if (cachedName != null) {
                result.put(areaCode, cachedName);
            } else {
                uncachedCodes.add(areaCode);
            }
        }

        // キャッシュにないデータをデータベースから取得
        if (!uncachedCodes.isEmpty()) {
            try {
                Map<String, String> dbResult = queryAreaNamesFromDatabase(uncachedCodes, template);
                
                // 結果をキャッシュに保存
                for (Map.Entry<String, String> entry : dbResult.entrySet()) {
                    String cacheKey = ImportSessionCache.generateKey("AREA", "AREA_CODE", entry.getKey());
                    cache.put(cacheKey, entry.getValue());
                }
                
                result.putAll(dbResult);
                logger.debug("エリア名称取得完了: DB取得={}, キャッシュ取得={}", 
                           dbResult.size(), uniqueAreaCodes.size() - uncachedCodes.size());
            } catch (Exception e) {
                logger.warn("エリア名称取得中にエラーが発生しました: {}", e.getMessage(), e);
            }
        }

        return result;
    }

    /**
     * グループコードからグループ名称を一括取得
     * 
     * @param groupCodes グループコードのリスト
     * @param template JDBCテンプレート
     * @param cache セッションキャッシュ
     * @return グループコードとグループ名称のマップ
     */
    public Map<String, String> getGroupNames(List<String> groupCodes, 
                                            JdbcTemplate template, 
                                            ImportSessionCache cache) {
        if (groupCodes == null || groupCodes.isEmpty()) {
            return new HashMap<>();
        }

        // 重複を除去
        List<String> uniqueGroupCodes = groupCodes.stream()
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (uniqueGroupCodes.isEmpty()) {
            return new HashMap<>();
        }

        // キャッシュから取得を試行
        Map<String, String> result = new HashMap<>();
        List<String> uncachedCodes = new ArrayList<>();

        for (String groupCode : uniqueGroupCodes) {
            String cacheKey = ImportSessionCache.generateKey("GROUP", "GROUP_CODE", groupCode);
            String cachedName = cache.get(cacheKey);
            if (cachedName != null) {
                result.put(groupCode, cachedName);
            } else {
                uncachedCodes.add(groupCode);
            }
        }

        // キャッシュにないデータをデータベースから取得
        if (!uncachedCodes.isEmpty()) {
            try {
                Map<String, String> dbResult = queryGroupNamesFromDatabase(uncachedCodes, template);
                
                // 結果をキャッシュに保存
                for (Map.Entry<String, String> entry : dbResult.entrySet()) {
                    String cacheKey = ImportSessionCache.generateKey("GROUP", "GROUP_CODE", entry.getKey());
                    cache.put(cacheKey, entry.getValue());
                }
                
                result.putAll(dbResult);
                logger.debug("グループ名称取得完了: DB取得={}, キャッシュ取得={}", 
                           dbResult.size(), uniqueGroupCodes.size() - uncachedCodes.size());
            } catch (Exception e) {
                logger.warn("グループ名称取得中にエラーが発生しました: {}", e.getMessage(), e);
            }
        }

        return result;
    }

    /**
     * ユニットコードからユニット名称を一括取得
     * 
     * @param unitCodes ユニットコードのリスト
     * @param template JDBCテンプレート
     * @param cache セッションキャッシュ
     * @return ユニットコードとユニット名称のマップ
     */
    public Map<String, String> getUnitNames(List<String> unitCodes, 
                                           JdbcTemplate template, 
                                           ImportSessionCache cache) {
        if (unitCodes == null || unitCodes.isEmpty()) {
            return new HashMap<>();
        }

        // 重複を除去
        List<String> uniqueUnitCodes = unitCodes.stream()
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (uniqueUnitCodes.isEmpty()) {
            return new HashMap<>();
        }

        // キャッシュから取得を試行
        Map<String, String> result = new HashMap<>();
        List<String> uncachedCodes = new ArrayList<>();

        for (String unitCode : uniqueUnitCodes) {
            String cacheKey = ImportSessionCache.generateKey("UNIT", "UNIT_CODE", unitCode);
            String cachedName = cache.get(cacheKey);
            if (cachedName != null) {
                result.put(unitCode, cachedName);
            } else {
                uncachedCodes.add(unitCode);
            }
        }

        // キャッシュにないデータをデータベースから取得
        if (!uncachedCodes.isEmpty()) {
            try {
                Map<String, String> dbResult = queryUnitNamesFromDatabase(uncachedCodes, template);
                
                // 結果をキャッシュに保存
                for (Map.Entry<String, String> entry : dbResult.entrySet()) {
                    String cacheKey = ImportSessionCache.generateKey("UNIT", "UNIT_CODE", entry.getKey());
                    cache.put(cacheKey, entry.getValue());
                }
                
                result.putAll(dbResult);
                logger.debug("ユニット名称取得完了: DB取得={}, キャッシュ取得={}", 
                           dbResult.size(), uniqueUnitCodes.size() - uncachedCodes.size());
            } catch (Exception e) {
                logger.warn("ユニット名称取得中にエラーが発生しました: {}", e.getMessage(), e);
            }
        }

        return result;
    }

    /**
     * データベースからエリア名称を取得
     */
    private Map<String, String> queryAreaNamesFromDatabase(List<String> areaCodes, 
                                                          JdbcTemplate template) throws SQLException {
        String placeholders = String.join(",", Collections.nCopies(areaCodes.size(), "?"));
        String sql = String.format("""
            SELECT 
                AREA_CODE,
                REGEXP_REPLACE(area_mei_tnshk_kanji, '\\s', '', 'g') as area_name
            FROM M_SOSHIKIAREAMST
            WHERE AREA_CODE IN (%s)
            AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= KSHB
            AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= SHRYB
            AND SHIYO_KNSH_KUBUN = '0'
            """, placeholders);

        List<Map<String, String>> results = template.query(sql, areaCodes.toArray(), rs -> {
            Map<String, String> result = new HashMap<>();
            result.put(rs.getString("AREA_CODE"), rs.getString("area_name"));
            return result;
        });
        
        Map<String, String> finalResult = new HashMap<>();
        for (Map<String, String> map : results) {
            finalResult.putAll(map);
        }
        return finalResult;
    }

    /**
     * データベースからグループ名称を取得
     */
    private Map<String, String> queryGroupNamesFromDatabase(List<String> groupCodes, 
                                                           JdbcTemplate template) throws SQLException {
        String placeholders = String.join(",", Collections.nCopies(groupCodes.size(), "?"));
        String sql = String.format("""
            SELECT 
                GROUP_CODE,
                REGEXP_REPLACE(group_mei_tnshk_kanji, '\\s', '', 'g') as group_name
            FROM M_GROUPMST
            WHERE GROUP_CODE IN (%s)
            AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= KSHB
            AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= SHRYB
            AND SHIYO_KNSH_KUBUN = '0'
            """, placeholders);

        List<Map<String, String>> results = template.query(sql, groupCodes.toArray(), rs -> {
            Map<String, String> result = new HashMap<>();
            result.put(rs.getString("GROUP_CODE"), rs.getString("group_name"));
            return result;
        });
        
        Map<String, String> finalResult = new HashMap<>();
        for (Map<String, String> map : results) {
            finalResult.putAll(map);
        }
        return finalResult;
    }

    /**
     * データベースからユニット名称を取得
     */
    private Map<String, String> queryUnitNamesFromDatabase(List<String> unitCodes, 
                                                          JdbcTemplate template) throws SQLException {
        String placeholders = String.join(",", Collections.nCopies(unitCodes.size(), "?"));
        String sql = String.format("""
            SELECT 
                UNIT_CODE,
                REGEXP_REPLACE(unit_mei_tnshk_kanji, '\\s', '', 'g') as unit_name
            FROM M_UNITMST
            WHERE UNIT_CODE IN (%s)
            AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') >= KSHB
            AND TO_CHAR(CURRENT_DATE, 'YYYYMMDD') <= SHRYB
            AND SHIYO_KNSH_KUBUN = '0'
            """, placeholders);

        List<Map<String, String>> results = template.query(sql, unitCodes.toArray(), rs -> {
            Map<String, String> result = new HashMap<>();
            result.put(rs.getString("UNIT_CODE"), rs.getString("unit_name"));
            return result;
        });
        
        Map<String, String> finalResult = new HashMap<>();
        for (Map<String, String> map : results) {
            finalResult.putAll(map);
        }
        return finalResult;
    }
}
