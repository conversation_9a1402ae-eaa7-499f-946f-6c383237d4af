package com.ms.bp.shared.common.io.facade;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.infrastructure.external.s3.S3Service;
import com.ms.bp.shared.common.io.converter.DatabaseMappable;
import com.ms.bp.shared.common.io.model.ExportResult;
import com.ms.bp.shared.common.io.model.ImportResult;
import com.ms.bp.shared.common.io.options.ExportOptions;
import com.ms.bp.shared.common.io.options.ImportOptions;
import com.ms.bp.shared.common.io.template.ImportTemplate;
import com.ms.bp.shared.common.io.template.ExportTemplate;
import com.ms.bp.shared.common.io.validator.DataValidator;
import com.ms.bp.domain.file.base.DataExpander;
import com.ms.bp.domain.file.base.ExpansionContext;

import java.io.IOException;
import java.io.InputStream;
import java.sql.SQLException;
import java.util.Map;
import java.util.function.Function;

/**
 * データインポート・エクスポートファサード（ファサードパターン）- JD<PERSON>版
 * クライアント向けに簡略化されたインターフェースを提供
 */
public class DataImportExportFacade {
    private final Context lambdaContext;
    private final S3Service s3Service;

    /**
     * デフォルトコンストラクタ - 通常の実行時に使用
     * @param lambdaContext Lambdaコンテキスト
     */
    public DataImportExportFacade(
            Context lambdaContext) {
        this.lambdaContext = lambdaContext;
        this.s3Service = new S3Service();
    }

    /**
     * テスト用コンストラクタ - S3Serviceを外部から注入
     * @param lambdaContext Lambdaコンテキスト
     * @param s3Service S3Service
     */
    public DataImportExportFacade(Context lambdaContext, com.ms.bp.infrastructure.external.s3.S3Service s3Service) {
        this.lambdaContext = lambdaContext;
        this.s3Service = s3Service;
    }

    /**
     * データをインポート（DTOベース）
     * パフォーマンス最適化：DatabaseMappableを実装するDTOのみを受け入れ
     * @param inputStream 入力ストリーム
     * @param dtoClass DTOクラス
     * @param options インポートオプション
     * @param validator データ検証器
     * @return インポート結果
     */
    public <T extends DatabaseMappable> ImportResult importData(InputStream inputStream,
                                       Class<T> dtoClass,
                                       ImportOptions options,
                                       DataValidator validator) throws SQLException, IOException {
        return importData(inputStream, dtoClass, options, validator, null);
    }

    /**
     * データをインポート（カスタムテンプレート対応版）
     * @param inputStream 入力ストリーム
     * @param dtoClass DTOクラス
     * @param options インポートオプション
     * @param validator データ検証器
     * @param customTemplate カスタムインポートテンプレート（nullの場合はデフォルトテンプレートを使用）
     * @return インポート結果
     */
    public <T extends DatabaseMappable> ImportResult importData(InputStream inputStream,
                                       Class<T> dtoClass,
                                       ImportOptions options,
                                       DataValidator validator,
                                       ImportTemplate<T> customTemplate) throws SQLException, IOException {
        ImportTemplate<T> template;
        if (customTemplate != null) {
            template = customTemplate;
        } else {
            // 匿名クラスでImportTemplateを実装（S3Serviceを注入）
            template = new ImportTemplate<T>(s3Service) {};
        }
        return template.executeImport(inputStream, dtoClass,
                options, lambdaContext, validator);
    }

    /**
     * データをエクスポート（統一版）
     * データ展開機能の有無に関わらず統一的に処理
     *
     * @param sqlTemplate SQLテンプレート
     * @param params SQLパラメータ
     * @param dataFormatter データ格式化関数（オプション）
     * @param options エクスポートオプション
     * @param expander データ展開処理器（nullの場合は展開なし）
     * @param expansionContext 展開処理のコンテキスト（expanderがnullの場合は無視）
     * @return エクスポート結果
     */
    public ExportResult exportData(String sqlTemplate,
                                   Map<String, Object> params,
                                   Function<Map<String, Object>, Map<String, Object>> dataFormatter,
                                   ExportOptions options,
                                   DataExpander expander,
                                   ExpansionContext expansionContext) {
        // 匿名クラスでExportTemplateを実装
        ExportTemplate<Map<String, Object>> template = new ExportTemplate<Map<String, Object>>() {};

        // 統一されたエクスポート実行（データ展開の有無を自動判定）
        return template.executeExport(sqlTemplate, params, expander, dataFormatter,
                                    options, lambdaContext, expansionContext);
    }

}


