package com.ms.bp.shared.common.io.cache;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * インポートセッション用簡易キャッシュ
 * 単一のインポート処理中にマスタデータの重複クエリを避けるための軽量キャッシュ
 * 
 * 特徴：
 * - インポート処理期間中のみ有効
 * - スレッドセーフ
 * - メモリ使用量を抑制するため自動クリア機能付き
 */
public class ImportSessionCache {
    private static final Logger logger = LoggerFactory.getLogger(ImportSessionCache.class);
    
    // キャッシュストレージ（スレッドセーフ）
    private final Map<String, Object> cache = new ConcurrentHashMap<>();
    

    /**
     * キャッシュからデータを取得
     * 
     * @param key キャッシュキー
     * @return キャッシュされたデータ、存在しない場合はnull
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String key) {
        return (T) cache.get(key);
    }
    
    /**
     * データをキャッシュに保存
     * 
     * @param key キャッシュキー
     * @param value 保存するデータ
     */
    public void put(String key, Object value) {
        if (key != null && value != null) {
            cache.put(key, value);
            logger.debug("キャッシュ保存: key={}, valueType={}", key, value.getClass().getSimpleName());
        }
    }
    
    /**
     * 複数のデータを一括でキャッシュに保存
     * 
     * @param dataMap 保存するデータのMap
     */
    public void putAll(Map<String, Object> dataMap) {
        if (dataMap != null && !dataMap.isEmpty()) {
            cache.putAll(dataMap);
            logger.debug("キャッシュ一括保存: 件数={}", dataMap.size());
        }
    }
    
    /**
     * キャッシュをクリア
     */
    public void clear() {
        int size = cache.size();
        cache.clear();
        logger.debug("キャッシュクリア完了: クリア件数={}", size);
    }

    /**
     * キャッシュキーを生成するヘルパーメソッド
     * 
     * @param masterType マスタタイプ（例：AREA, GROUP, UNIT）
     * @param keyField キーフィールド名
     * @param keyValue キー値
     * @return 生成されたキャッシュキー
     */
    public static String generateKey(String masterType, String keyField, String keyValue) {
        return String.format("%s:%s:%s", masterType, keyField, keyValue);
    }

}
