package com.ms.bp.shared.common.io.cache;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * インポートセッション用簡易キャッシュ
 * 単一のインポート処理中にマスタデータの重複クエリを避けるための軽量キャッシュ
 * 
 * 特徴：
 * - インポート処理期間中のみ有効
 * - スレッドセーフ
 * - メモリ使用量を抑制するため自動クリア機能付き
 */
public class ImportSessionCache {
    private static final Logger logger = LoggerFactory.getLogger(ImportSessionCache.class);
    
    // キャッシュストレージ（スレッドセーフ）
    private final Map<String, Object> cache = new ConcurrentHashMap<>();
    
    // キャッシュ統計情報
    private int hitCount = 0;
    private int missCount = 0;
    
    /**
     * キャッシュからデータを取得
     * 
     * @param key キャッシュキー
     * @return キャッシュされたデータ、存在しない場合はnull
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String key) {
        T value = (T) cache.get(key);
        if (value != null) {
            hitCount++;
            logger.debug("キャッシュヒット: key={}", key);
        } else {
            missCount++;
            logger.debug("キャッシュミス: key={}", key);
        }
        return value;
    }
    
    /**
     * データをキャッシュに保存
     * 
     * @param key キャッシュキー
     * @param value 保存するデータ
     */
    public void put(String key, Object value) {
        if (key != null && value != null) {
            cache.put(key, value);
            logger.debug("キャッシュ保存: key={}, valueType={}", key, value.getClass().getSimpleName());
        }
    }
    
    /**
     * 複数のデータを一括でキャッシュに保存
     * 
     * @param dataMap 保存するデータのMap
     */
    public void putAll(Map<String, Object> dataMap) {
        if (dataMap != null && !dataMap.isEmpty()) {
            cache.putAll(dataMap);
            logger.debug("キャッシュ一括保存: 件数={}", dataMap.size());
        }
    }
    
    /**
     * キャッシュをクリア
     */
    public void clear() {
        int size = cache.size();
        cache.clear();
        hitCount = 0;
        missCount = 0;
        logger.debug("キャッシュクリア完了: クリア件数={}", size);
    }
    
    /**
     * キャッシュサイズを取得
     * 
     * @return 現在のキャッシュエントリ数
     */
    public int size() {
        return cache.size();
    }
    
    /**
     * キャッシュ統計情報を取得
     * 
     * @return 統計情報のMap
     */
    public Map<String, Integer> getStatistics() {
        Map<String, Integer> stats = new HashMap<>();
        stats.put("hitCount", hitCount);
        stats.put("missCount", missCount);
        stats.put("totalRequests", hitCount + missCount);
        stats.put("cacheSize", cache.size());
        return stats;
    }
    
    /**
     * キャッシュヒット率を取得
     * 
     * @return ヒット率（0.0-1.0）
     */
    public double getHitRate() {
        int total = hitCount + missCount;
        return total > 0 ? (double) hitCount / total : 0.0;
    }
    
    /**
     * キャッシュキーを生成するヘルパーメソッド
     * 
     * @param masterType マスタタイプ（例：AREA, GROUP, UNIT）
     * @param keyField キーフィールド名
     * @param keyValue キー値
     * @return 生成されたキャッシュキー
     */
    public static String generateKey(String masterType, String keyField, String keyValue) {
        return String.format("%s:%s:%s", masterType, keyField, keyValue);
    }
    
    /**
     * 複数キー用のキャッシュキーを生成
     * 
     * @param masterType マスタタイプ
     * @param keyValues キー値のリスト（カンマ区切りで結合）
     * @return 生成されたキャッシュキー
     */
    public static String generateBatchKey(String masterType, String keyValues) {
        return String.format("%s:BATCH:%s", masterType, keyValues);
    }
}
