package com.ms.bp.domain.file.planmaster;

import com.ms.bp.domain.file.model.PlanMasterImportData;
import com.ms.bp.shared.common.io.template.ImportTemplate;
import com.ms.bp.shared.common.io.cache.ImportSessionCache;
import com.ms.bp.shared.common.io.enrichment.MasterDataEnrichmentUtil;
import com.ms.bp.shared.common.db.JdbcTemplate;
import com.ms.bp.infrastructure.external.s3.S3Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 次年度計画マスタ専用インポートテンプレート
 * マスタデータ補完機能を実装
 * 
 * 補完対象：
 * - エリアコード → エリア名称
 * - グループコード → グループ名称  
 * - ユニットコード → ユニット名称
 * - 移管先エリアコード → 移管先エリア名称
 * - 移管先グループコード → 移管先グループ名称
 */
public class PlanMasterImportTemplate extends ImportTemplate<PlanMasterImportData> {
    private static final Logger logger = LoggerFactory.getLogger(PlanMasterImportTemplate.class);

    /**
     * デフォルトコンストラクタ
     */
    public PlanMasterImportTemplate() {
        super();
    }

    /**
     * テスト用コンストラクタ
     */
    public PlanMasterImportTemplate(S3Service s3Service) {
        super(s3Service);
    }

    @Override
    protected void enrichMasterData(List<PlanMasterImportData> dtos, 
                                   JdbcTemplate template, 
                                   ImportSessionCache cache) {
        if (dtos == null || dtos.isEmpty()) {
            return;
        }

        logger.debug("次年度計画マスタのマスタデータ補完開始: 対象DTO数={}", dtos.size());

        try {
            // 1. 必要なコードを収集
            Set<String> areaCodes = new HashSet<>();
            Set<String> groupCodes = new HashSet<>();
            Set<String> unitCodes = new HashSet<>();

            for (PlanMasterImportData dto : dtos) {
                // 基本フィールドのコードを収集
                if (dto.getAreaCode() != null) {
                    areaCodes.add(dto.getAreaCode());
                }
                if (dto.getGroupCode() != null) {
                    groupCodes.add(dto.getGroupCode());
                }
                if (dto.getUnitCode() != null) {
                    unitCodes.add(dto.getUnitCode());
                }

                // 移管先フィールドのコードを収集
                if (dto.getIkansakiAreaCode() != null && !dto.getIkansakiAreaCode().trim().isEmpty()) {
                    areaCodes.add(dto.getIkansakiAreaCode());
                }
                if (dto.getIkansakiGroupCode() != null && !dto.getIkansakiGroupCode().trim().isEmpty()) {
                    groupCodes.add(dto.getIkansakiGroupCode());
                }
            }

            // 2. マスタデータを一括取得
            Map<String, String> areaNames = MasterDataEnrichmentUtil.getAreaNames(
                new ArrayList<>(areaCodes), template, cache);
            Map<String, String> groupNames = MasterDataEnrichmentUtil.getGroupNames(
                new ArrayList<>(groupCodes), template, cache);
            Map<String, String> unitNames = MasterDataEnrichmentUtil.getUnitNames(
                new ArrayList<>(unitCodes), template, cache);

            // 3. 各DTOにマスタデータを適用
            for (PlanMasterImportData dto : dtos) {
                Map<String, Object> masterDataMap = new HashMap<>();

                // エリア名称を設定
                if (dto.getAreaCode() != null && areaNames.containsKey(dto.getAreaCode())) {
                    masterDataMap.put("AREA_NAME_" + dto.getAreaCode(), areaNames.get(dto.getAreaCode()));
                }

                // グループ名称を設定
                if (dto.getGroupCode() != null && groupNames.containsKey(dto.getGroupCode())) {
                    masterDataMap.put("GROUP_NAME_" + dto.getGroupCode(), groupNames.get(dto.getGroupCode()));
                }

                // ユニット名称を設定
                if (dto.getUnitCode() != null && unitNames.containsKey(dto.getUnitCode())) {
                    masterDataMap.put("UNIT_NAME_" + dto.getUnitCode(), unitNames.get(dto.getUnitCode()));
                }

                // 移管先エリア名称を設定
                if (dto.getIkansakiAreaCode() != null && areaNames.containsKey(dto.getIkansakiAreaCode())) {
                    masterDataMap.put("AREA_NAME_" + dto.getIkansakiAreaCode(), areaNames.get(dto.getIkansakiAreaCode()));
                }

                // 移管先グループ名称を設定
                if (dto.getIkansakiGroupCode() != null && groupNames.containsKey(dto.getIkansakiGroupCode())) {
                    masterDataMap.put("GROUP_NAME_" + dto.getIkansakiGroupCode(), groupNames.get(dto.getIkansakiGroupCode()));
                }

                // DTOにマスタデータを適用
                dto.applyMasterData(masterDataMap);
            }

            logger.debug("次年度計画マスタのマスタデータ補完完了: エリア名称={}, グループ名称={}, ユニット名称={}", 
                        areaNames.size(), groupNames.size(), unitNames.size());

        } catch (Exception e) {
            logger.warn("次年度計画マスタのマスタデータ補完中にエラーが発生しました: {}", e.getMessage(), e);
            // エラーが発生しても処理は継続（マスタデータ補完は必須ではない）
        }
    }
}
