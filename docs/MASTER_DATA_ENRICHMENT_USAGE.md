# マスタデータ補完機能使用ガイド（簡化版）

## 概要

データインポート処理中に、マスタテーブルから名称情報を自動取得して補完する機能です。
既存のAbstractImportServiceアーキテクチャを維持し、新しいTemplate子クラスを作成せずに実装します。

## 主な特徴

1. **既存アーキテクチャ維持**: AbstractImportServiceとImportTemplateの匿名クラスを継続使用
2. **メソッドオーバーライド方式**: 各ImportServiceがenrichMasterDataメソッドをオーバーライド
3. **自動統合**: AbstractImportServiceが自動的にマスタデータ補完を呼び出し
4. **簡単キャッシュ**: 単一インポート処理中の重複クエリを回避
5. **容错性**: マスタデータ取得失敗時も正常なインポート処理を継続

## 実装手順

### 1. DTOクラスの拡張

```java
@Data
public class YourImportData implements DatabaseMappable {
    // 既存フィールド
    private String areaCode;
    private String groupCode;
    
    // マスタデータ補完フィールド
    private String areaName;
    private String groupName;
    
    @Override
    public boolean requiresMasterDataEnrichment() {
        return true; // マスタデータ補完が必要
    }
    
    @Override
    public void applyMasterData(Map<String, Object> masterDataMap) {
        if (masterDataMap == null) return;
        
        // エリア名称を設定
        if (areaCode != null && masterDataMap.containsKey("AREA_NAME_" + areaCode)) {
            this.areaName = (String) masterDataMap.get("AREA_NAME_" + areaCode);
        }
        
        // グループ名称を設定
        if (groupCode != null && masterDataMap.containsKey("GROUP_NAME_" + groupCode)) {
            this.groupName = (String) masterDataMap.get("GROUP_NAME_" + groupCode);
        }
    }
}
```

### 2. ImportServiceでのマスタデータ補完実装

```java
public class YourImportService extends AbstractImportService<YourImportData> {
    private static final Logger logger = LoggerFactory.getLogger(YourImportService.class);

    // doExecuteメソッドの実装は不要（AbstractImportServiceで自動的にマスタデータ補完が呼び出される）

    /**
     * マスタデータ補完処理（AbstractImportServiceのメソッドをオーバーライド）
     */
    @Override
    protected void enrichMasterData(List<YourImportData> dtos,
                                   JdbcTemplate template,
                                   ImportSessionCache cache) {
        if (dtos == null || dtos.isEmpty()) return;

        logger.debug("マスタデータ補完開始: 対象DTO数={}", dtos.size());

        try {
            // 1. 必要なコードを収集
            Set<String> areaCodes = new HashSet<>();
            Set<String> groupCodes = new HashSet<>();

            for (YourImportData dto : dtos) {
                if (dto.getAreaCode() != null) {
                    areaCodes.add(dto.getAreaCode());
                }
                if (dto.getGroupCode() != null) {
                    groupCodes.add(dto.getGroupCode());
                }
            }

            // 2. マスタデータを一括取得
            Map<String, String> areaNames = MasterDataEnrichmentUtil.getAreaNames(
                new ArrayList<>(areaCodes), template, cache);
            Map<String, String> groupNames = MasterDataEnrichmentUtil.getGroupNames(
                new ArrayList<>(groupCodes), template, cache);

            // 3. 各DTOにマスタデータを適用
            for (YourImportData dto : dtos) {
                Map<String, Object> masterDataMap = new HashMap<>();

                if (dto.getAreaCode() != null && areaNames.containsKey(dto.getAreaCode())) {
                    masterDataMap.put("AREA_NAME_" + dto.getAreaCode(), areaNames.get(dto.getAreaCode()));
                }

                if (dto.getGroupCode() != null && groupNames.containsKey(dto.getGroupCode())) {
                    masterDataMap.put("GROUP_NAME_" + dto.getGroupCode(), groupNames.get(dto.getGroupCode()));
                }

                dto.applyMasterData(masterDataMap);
            }

            logger.debug("マスタデータ補完完了: エリア名称={}, グループ名称={}",
                        areaNames.size(), groupNames.size());

        } catch (Exception e) {
            logger.warn("マスタデータ補完中にエラーが発生しました: {}", e.getMessage(), e);
        }
    }

    // 他の抽象メソッドの実装...
    @Override
    protected Class<YourImportData> getDTOClass() {
        return YourImportData.class;
    }

    @Override
    protected ImportOptions buildImportOptions() {
        // インポートオプションの構築
    }

    @Override
    protected DataValidator getDataValidator() {
        // データ検証器の構築
    }
}
```

## 簡化された使用方法

この実装により、以下の簡単な手順でマスタデータ補完機能を利用できます：

1. **DTOでインターフェース実装**: `requiresMasterDataEnrichment()`と`applyMasterData()`を実装
2. **ImportServiceでメソッドオーバーライド**: `enrichMasterData()`メソッドをオーバーライド
3. **自動実行**: AbstractImportServiceが自動的にマスタデータ補完を呼び出し

### 主な利点

- **doExecuteメソッドの実装不要**: AbstractImportServiceが自動処理
- **既存コードへの最小影響**: 既存のImportServiceに最小限の追加のみ
- **統一されたアーキテクチャ**: すべてのImportServiceで同じパターンを使用

## 利用可能なマスタデータ取得メソッド

### MasterDataEnrichmentUtil

```java
// エリア名称を一括取得
Map<String, String> areaNames = MasterDataEnrichmentUtil.getAreaNames(
    areaCodes, template, cache);

// グループ名称を一括取得  
Map<String, String> groupNames = MasterDataEnrichmentUtil.getGroupNames(
    groupCodes, template, cache);

// ユニット名称を一括取得
Map<String, String> unitNames = MasterDataEnrichmentUtil.getUnitNames(
    unitCodes, template, cache);
```

## カスタムマスタデータ取得

独自のマスタテーブルから名称を取得する場合：

```java
private Map<String, String> getCustomMasterNames(List<String> codes, 
                                               JdbcTemplate template, 
                                               ImportSessionCache cache) {
    // キャッシュチェック
    Map<String, String> result = new HashMap<>();
    List<String> uncachedCodes = new ArrayList<>();
    
    for (String code : codes) {
        String cacheKey = ImportSessionCache.generateKey("CUSTOM", "CODE", code);
        String cachedName = cache.get(cacheKey);
        if (cachedName != null) {
            result.put(code, cachedName);
        } else {
            uncachedCodes.add(code);
        }
    }
    
    // データベースクエリ
    if (!uncachedCodes.isEmpty()) {
        String placeholders = String.join(",", Collections.nCopies(uncachedCodes.size(), "?"));
        String sql = String.format("SELECT code, name FROM your_master_table WHERE code IN (%s)", placeholders);
        
        List<Map<String, String>> dbResults = template.query(sql, uncachedCodes.toArray(), rs -> {
            Map<String, String> row = new HashMap<>();
            row.put(rs.getString("code"), rs.getString("name"));
            return row;
        });
        
        // キャッシュに保存
        for (Map<String, String> row : dbResults) {
            for (Map.Entry<String, String> entry : row.entrySet()) {
                String cacheKey = ImportSessionCache.generateKey("CUSTOM", "CODE", entry.getKey());
                cache.put(cacheKey, entry.getValue());
                result.put(entry.getKey(), entry.getValue());
            }
        }
    }
    
    return result;
}
```

## パフォーマンス考慮事項

1. **バッチサイズ**: 大量データの場合、適切なバッチサイズを設定
2. **キャッシュ効果**: 同一インポート内での重複コードはキャッシュから高速取得
3. **エラーハンドリング**: マスタデータ取得失敗時も処理継続

## ログ出力例

```
2024-01-01 10:00:01 DEBUG - 次年度計画マスタのマスタデータ補完開始: 対象DTO数=100
2024-01-01 10:00:02 DEBUG - エリア名称取得完了: DB取得=5, キャッシュ取得=95
2024-01-01 10:00:02 DEBUG - グループ名称取得完了: DB取得=3, キャッシュ取得=97
2024-01-01 10:00:02 DEBUG - 次年度計画マスタのマスタデータ補完完了: エリア名称=5, グループ名称=3, ユニット名称=0
2024-01-01 10:00:10 INFO  - インポートセッション完了 - キャッシュ統計: ヒット数=192, ミス数=8, ヒット率=96.00%
```
